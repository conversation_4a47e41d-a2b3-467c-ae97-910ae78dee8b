import asyncio
import json
import re
import tkinter as tk
from tkinter import messagebox, scrolledtext, simpledialog, filedialog
import threading
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

class ResultViewer:
    """结果查看器 - 使用GUI对话框显示"""
    
    def __init__(self):
        self.root = None
        self.setup_gui()
    
    def setup_gui(self):
        """初始化GUI"""
        self.root = tk.Tk()
        self.root.withdraw()  # 隐藏主窗口
        
    def show_message(self, title, message, msg_type="info"):
        """显示消息对话框"""
        if msg_type == "info":
            messagebox.showinfo(title, message)
        elif msg_type == "warning":
            messagebox.showwarning(title, message)
        elif msg_type == "error":
            messagebox.showerror(title, message)
    
    def show_text_dialog(self, title, content, editable=True):
        """显示文本对话框"""
        dialog = tk.Toplevel(self.root)
        dialog.title(title)
        dialog.geometry("800x600")
        dialog.transient(self.root)
        dialog.grab_set()
        
        # 创建文本框
        text_frame = tk.Frame(dialog)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        text_widget = scrolledtext.ScrolledText(
            text_frame, 
            wrap=tk.WORD,
            font=("Consolas", 10),
            state=tk.NORMAL if editable else tk.DISABLED
        )
        text_widget.pack(fill=tk.BOTH, expand=True)
        text_widget.insert(tk.END, content)
        
        # 按钮框架
        button_frame = tk.Frame(dialog)
        button_frame.pack(fill=tk.X, padx=10, pady=5)
        
        result = {"content": content, "action": None}
        
        def on_save():
            result["content"] = text_widget.get(1.0, tk.END).strip()
            result["action"] = "save"
            dialog.destroy()
        
        def on_export():
            filename = filedialog.asksaveasfilename(
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
            )
            if filename:
                try:
                    with open(filename, 'w', encoding='utf-8') as f:
                        f.write(text_widget.get(1.0, tk.END))
                    messagebox.showinfo("成功", f"文件已保存到: {filename}")
                except Exception as e:
                    messagebox.showerror("错误", f"保存失败: {e}")
        
        def on_clear():
            if messagebox.askyesno("确认", "确定要清空内容吗？"):
                text_widget.delete(1.0, tk.END)
        
        def on_close():
            result["content"] = text_widget.get(1.0, tk.END).strip()
            result["action"] = "close"
            dialog.destroy()
        
        # 添加按钮
        if editable:
            tk.Button(button_frame, text="保存", command=on_save).pack(side=tk.LEFT, padx=5)
        
        tk.Button(button_frame, text="导出", command=on_export).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="清空", command=on_clear).pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="关闭", command=on_close).pack(side=tk.RIGHT, padx=5)
        
        # 等待对话框关闭
        dialog.wait_window()
        return result
    
    def show_table_structure(self, schema_data):
        """显示表结构对话框"""
        dialog = tk.Toplevel(self.root)
        dialog.title("数据库表结构")
        dialog.geometry("900x700")
        dialog.transient(self.root)
        dialog.grab_set()
        
        # 创建笔记本控件（标签页）
        from tkinter import ttk
        notebook = ttk.Notebook(dialog)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 解析schema数据
        try:
            if isinstance(schema_data, str):
                data = json.loads(schema_data)
            else:
                data = schema_data
            
            schema_text = data.get("schema", "")
            
            # 按表分组显示
            tables = self.parse_table_info(schema_text)
            
            for table_name, table_info in tables.items():
                # 为每个表创建一个标签页
                frame = ttk.Frame(notebook)
                notebook.add(frame, text=table_name)
                
                text_widget = scrolledtext.ScrolledText(
                    frame,
                    wrap=tk.WORD,
                    font=("Consolas", 9)
                )
                text_widget.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
                text_widget.insert(tk.END, table_info)
                text_widget.config(state=tk.DISABLED)
        
        except Exception as e:
            # 如果解析失败，显示原始内容
            frame = ttk.Frame(notebook)
            notebook.add(frame, text="原始数据")
            
            text_widget = scrolledtext.ScrolledText(frame, wrap=tk.WORD)
            text_widget.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
            text_widget.insert(tk.END, str(schema_data))
            text_widget.config(state=tk.DISABLED)
        
        # 关闭按钮
        button_frame = tk.Frame(dialog)
        button_frame.pack(fill=tk.X, padx=10, pady=5)
        tk.Button(button_frame, text="关闭", command=dialog.destroy).pack(side=tk.RIGHT)
        
        dialog.wait_window()
    
    def parse_table_info(self, schema_text):
        """解析表信息"""
        tables = {}
        current_table = None
        current_content = []
        
        lines = schema_text.split('\n')
        for line in lines:
            if 'CREATE TABLE' in line.upper():
                if current_table:
                    tables[current_table] = '\n'.join(current_content)
                
                # 提取表名
                match = re.search(r'CREATE TABLE\s+`?(\w+)`?', line, re.IGNORECASE)
                if match:
                    current_table = match.group(1)
                    current_content = [line]
                else:
                    current_table = "未知表"
                    current_content = [line]
            else:
                if current_table:
                    current_content.append(line)
        
        if current_table:
            tables[current_table] = '\n'.join(current_content)
        
        return tables if tables else {"数据库结构": schema_text}

def clean_text(text):
    """清理文本，去除特殊符号和格式化字符"""
    if not text:
        return ""
    
    text = text.replace('\\"', '"').replace('\\n', '\n').replace('\\t', '  ')
    text = re.sub(r'^["\'\[\{]+|["\'\]\}]+$', '', text.strip())
    text = re.sub(r'[^\w\s\u4e00-\u9fff，。！？：；、（）【】""''—…]', '', text)
    text = re.sub(r'\s+', ' ', text).strip()
    
    return text

def format_json_output(content_text):
    """格式化JSON输出为可读格式"""
    try:
        data = json.loads(content_text)
        
        if isinstance(data, dict):
            formatted_lines = []
            for key, value in data.items():
                if key == "result" and isinstance(value, str):
                    clean_value = clean_text(value)
                    formatted_lines.append(f"{key}: {clean_value}")
                elif key == "error":
                    formatted_lines.append(f"❌ 错误: {clean_text(str(value))}")
                elif key == "status":
                    status_icon = "✅" if value == "success" else "❌"
                    formatted_lines.append(f"{status_icon} 状态: {value}")
                else:
                    formatted_lines.append(f"{key}: {clean_text(str(value))}")
            
            return "\n".join(formatted_lines)
        else:
            return clean_text(str(data))
    except:
        return clean_text(content_text)

async def test_mcp_client():
    """测试MCP客户端"""
    # 在新线程中运行GUI
    viewer = ResultViewer()
    
    server_params = StdioServerParameters(
        command="python",
        args=["mcp_mysql_server.py"]
    )
    
    try:
        async with stdio_client(server_params) as (read, write):
            async with ClientSession(read, write) as session:
                # 初始化
                await session.initialize()
                viewer.show_message("连接状态", "🔗 MCP客户端已连接", "info")
                
                # 列出可用工具
                tools = await session.list_tools()
                tools_text = "📋 可用工具:\n\n"
                for tool in tools.tools:
                    tools_text += f"• {tool.name}: {clean_text(tool.description)}\n"
                
                viewer.show_text_dialog("可用工具", tools_text, editable=False)
                
                # 执行自然语言查询
                question = "sprs_rt_data表有多少行数据?"
                result = await session.call_tool(
                    "natural_query",
                    {"question": question}
                )
                
                # 显示查询结果
                formatted_results = []
                for content in result.content:
                    if content.type == "text":
                        formatted_output = format_json_output(content.text)
                        formatted_results.append(formatted_output)
                
                if formatted_results:
                    combined_result = '\n'.join(formatted_results)
                    result_data = viewer.show_text_dialog(
                        f"查询结果: {question}", 
                        combined_result, 
                        editable=True
                    )
                    
                    if result_data["action"] == "save":
                        viewer.show_message("保存", "查询结果已保存", "info")
                
                # 获取表结构
                schema_result = await session.call_tool("get_schema", {})
                
                schema_content = []
                for content in schema_result.content:
                    if content.type == "text":
                        schema_content.append(content.text)
                
                if schema_content:
                    combined_schema = '\n'.join(schema_content)
                    viewer.show_table_structure(combined_schema)
                
    except Exception as e:
        viewer.show_message("错误", f"连接失败: {e}", "error")
    
    finally:
        if viewer.root:
            viewer.root.quit()

def main():
    """主函数"""
    import queue
    
    # 创建结果队列
    result_queue = queue.Queue()
    
    def run_async():
        """在后台线程运行异步操作"""
        async def async_main():
            server_params = StdioServerParameters(
                command="python",
                args=["mcp_mysql_server.py"]
            )
            
            try:
                async with stdio_client(server_params) as (read, write):
                    async with ClientSession(read, write) as session:
                        # 初始化
                        await session.initialize()
                        result_queue.put(("message", "连接状态", "🔗 MCP客户端已连接", "info"))
                        
                        # 列出可用工具
                        tools = await session.list_tools()
                        tools_text = "📋 可用工具:\n\n"
                        for tool in tools.tools:
                            tools_text += f"• {tool.name}: {clean_text(tool.description)}\n"
                        
                        result_queue.put(("text_dialog", "可用工具", tools_text, False))
                        
                        # 执行自然语言查询
                        question = "sprs_rt_data表有多少行数据?"
                        result = await session.call_tool(
                            "natural_query",
                            {"question": question}
                        )
                        
                        # 显示查询结果
                        formatted_results = []
                        for content in result.content:
                            if content.type == "text":
                                formatted_output = format_json_output(content.text)
                                formatted_results.append(formatted_output)
                        
                        if formatted_results:
                            combined_result = '\n'.join(formatted_results)
                            result_queue.put(("text_dialog", f"查询结果: {question}", combined_result, True))
                        
                        # 获取表结构
                        schema_result = await session.call_tool("get_schema", {})
                        
                        schema_content = []
                        for content in schema_result.content:
                            if content.type == "text":
                                schema_content.append(content.text)
                        
                        if schema_content:
                            combined_schema = '\n'.join(schema_content)
                            result_queue.put(("table_structure", combined_schema))
                            
            except Exception as e:
                result_queue.put(("message", "错误", f"连接失败: {e}", "error"))
            
            result_queue.put(("done",))
        
        # 运行异步代码
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(async_main())
        loop.close()
    
    # 在主线程中创建GUI
    viewer = ResultViewer()
    
    # 启动后台线程
    thread = threading.Thread(target=run_async)
    thread.daemon = True
    thread.start()
    
    # 在主线程中处理GUI事件
    def process_queue():
        try:
            while True:
                item = result_queue.get_nowait()
                
                if item[0] == "message":
                    _, title, message, msg_type = item
                    viewer.show_message(title, message, msg_type)
                
                elif item[0] == "text_dialog":
                    _, title, content, editable = item
                    result_data = viewer.show_text_dialog(title, content, editable)
                    if result_data["action"] == "save":
                        viewer.show_message("保存", "内容已保存", "info")
                
                elif item[0] == "table_structure":
                    _, schema_data = item
                    viewer.show_table_structure(schema_data)
                
                elif item[0] == "done":
                    viewer.root.quit()
                    return
                    
        except queue.Empty:
            pass
        
        # 每100ms检查一次队列
        viewer.root.after(100, process_queue)
    
    # 开始处理队列
    viewer.root.after(100, process_queue)
    
    # 运行GUI主循环
    viewer.root.mainloop()

if __name__ == "__main__":
    main()


