import asyncio
import json
from typing import Any, Dict, List, Optional
import mysql.connector
from mysql.connector import <PERSON>rro<PERSON>
from langchain_ollama import OllamaLLM
from langchain_experimental.sql import SQLDatabase<PERSON>hain
from langchain_community.utilities import SQLDatabase
import mcp.server.stdio
from mcp.server import NotificationOptions, Server, InitializationOptions
from mcp.types import Resource, Tool, TextContent
from pydantic import AnyUrl
import mcp.types as types
from langchain.prompts import PromptTemplate

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'database': 'gtxsemi',
    'user': 'root',
    'password': '123456',
    'port': 3306
}

# 初始化LLM
llm = OllamaLLM(
    base_url="http://127.0.0.1:11434", 
    model="huihui_ai/deepseek-r1-abliterated:8b"
)

sql_prompt = PromptTemplate(
    input_variables=["input", "table_info", "dialect"],
    template="""Given an input question, create a syntactically correct {dialect} query to run.

Use the following format:

Question: "Question here"
SQLQuery: "SQL Query to run"

Only use the following tables:
{table_info}

Question: {input}
SQLQuery: """
)

# 创建MCP服务器
server = Server("mysql-langchain-mcp")

class MySQLLangChainService:
    def __init__(self):
        self.db_connection = None
        self.sql_db = None
        self.sql_chain = None
        
    async def connect_database(self):
        """连接MySQL数据库"""
        try:
            # 创建数据库连接字符串
            db_url = f"mysql+pymysql://{DB_CONFIG['user']}:{DB_CONFIG['password']}@{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}"
            
            # 创建SQLDatabase实例
            self.sql_db = SQLDatabase.from_uri(db_url)
            
            # 创建SQL查询链
            self.sql_chain = SQLDatabaseChain.from_llm(
                llm=llm,
                db=self.sql_db,
                prompt=sql_prompt,
                verbose=True,
                return_intermediate_steps=True
            )
            
            return True
        except Exception as e:
            print(f"数据库连接失败: {e}")
            return False
    
    async def execute_natural_query(self, question: str) -> Dict[str, Any]:
        """执行自然语言查询"""
        try:
            if not self.sql_chain:
                return {
                    "question": question,
                    "error": "Database not connected. Please connect first.",
                    "status": "error"
                }
            
            # 生成SQL查询
            chain_result = await self.sql_chain.ainvoke({"query": question})
            
            # 简化处理返回结果
            sql_query = "Generated by LLM"
            result = str(chain_result)
            
            return {
                "question": question,
                "sql_query": sql_query,
                "result": result,
                "status": "success"
            }
        except Exception as e:
            return {
                "question": question,
                "error": str(e),
                "status": "error"
            }
    
    async def get_table_schema(self, table_name: Optional[str] = None) -> Dict[str, Any]:
        """获取表结构信息"""
        try:
            if not self.sql_db:
                return {
                    "error": "Database not connected. Please connect first.",
                    "status": "error"
                }
            
            if table_name:
                schema_info = self.sql_db.get_table_info([table_name])
            else:
                schema_info = self.sql_db.get_table_info()
            
            return {
                "schema": schema_info,
                "status": "success"
            }
        except Exception as e:
            return {
                "error": str(e),
                "status": "error"
            }

# 创建服务实例
mysql_service = MySQLLangChainService()

@server.list_resources()
async def handle_list_resources() -> list[Resource]:
    """列出可用资源"""
    return [
        Resource(
            uri=AnyUrl("mysql://tables"),
            name="Database Tables",
            description="Available database tables and schema",
            mimeType="application/json",
        ),
        Resource(
            uri=AnyUrl("mysql://connection"),
            name="Database Connection",
            description="Database connection status",
            mimeType="application/json",
        )
    ]

@server.read_resource()
async def handle_read_resource(uri: AnyUrl) -> str:
    """读取资源内容"""
    if uri == "mysql://tables":
        schema_info = await mysql_service.get_table_schema()
        return json.dumps(schema_info, indent=2, ensure_ascii=False)
    elif uri == "mysql://connection":
        status = {"connected": mysql_service.sql_db is not None}
        return json.dumps(status, indent=2)
    else:
        raise ValueError(f"Unknown resource: {uri}")

@server.list_tools()
async def handle_list_tools() -> list[Tool]:
    """列出可用工具"""
    return [
        Tool(
            name="natural_query",
            description="Execute natural language queries against MySQL database",
            inputSchema={
                "type": "object",
                "properties": {
                    "question": {
                        "type": "string",
                        "description": "Natural language question about the database"
                    }
                },
                "required": ["question"]
            }
        ),
        Tool(
            name="get_schema",
            description="Get database table schema information",
            inputSchema={
                "type": "object",
                "properties": {
                    "table_name": {
                        "type": "string",
                        "description": "Specific table name (optional)"
                    }
                }
            }
        ),
        Tool(
            name="execute_sql",
            description="Execute raw SQL query",
            inputSchema={
                "type": "object",
                "properties": {
                    "sql": {
                        "type": "string",
                        "description": "SQL query to execute"
                    }
                },
                "required": ["sql"]
            }
        )
    ]

@server.call_tool()
async def handle_call_tool(name: str, arguments: dict) -> list[types.TextContent]:
    """处理工具调用"""
    if name == "natural_query":
        question = arguments.get("question")
        if not question:
            return [types.TextContent(
                type="text",
                text=json.dumps({"error": "Question parameter is required", "status": "error"}, indent=2, ensure_ascii=False)
            )]
        result = await mysql_service.execute_natural_query(question)
        return [types.TextContent(
            type="text",
            text=json.dumps(result, indent=2, ensure_ascii=False)
        )]
    
    elif name == "get_schema":
        table_name = arguments.get("table_name")
        result = await mysql_service.get_table_schema(table_name)
        return [types.TextContent(
            type="text",
            text=json.dumps(result, indent=2, ensure_ascii=False)
        )]
    
    elif name == "execute_sql":
        sql = arguments.get("sql")
        if not sql:
            return [types.TextContent(
                type="text",
                text=json.dumps({"error": "SQL parameter is required", "status": "error"}, indent=2, ensure_ascii=False)
            )]
        
        if not mysql_service.sql_db:
            return [types.TextContent(
                type="text",
                text=json.dumps({"error": "Database not connected", "status": "error"}, indent=2, ensure_ascii=False)
            )]
        
        try:
            result = mysql_service.sql_db.run(sql)
            response = {
                "sql": sql,
                "result": result,
                "status": "success"
            }
        except Exception as e:
            response = {
                "sql": sql,
                "error": str(e),
                "status": "error"
            }
        
        return [types.TextContent(
            type="text",
            text=json.dumps(response, indent=2, ensure_ascii=False)
        )]
    
    else:
        raise ValueError(f"Unknown tool: {name}")

async def main():
    """主函数"""
    # 连接数据库
    print("正在连接MySQL数据库...")
    if await mysql_service.connect_database():
        print("数据库连接成功!")
    else:
        print("数据库连接失败!")
        return
    
    # 启动MCP服务器
    async with mcp.server.stdio.stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name="mysql-langchain-mcp",
                server_version="1.0.0",
                capabilities=server.get_capabilities(
                    notification_options=NotificationOptions(),
                    experimental_capabilities={},
                ),
            ),
        )

if __name__ == "__main__":
    asyncio.run(main())































