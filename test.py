import langchain
print(langchain.__version__)  # 显示 LangChain 版本

from langchain_ollama import OllamaLLM
# from langchain_core.output_parsers import StrOutputParser
# from langchain_core.prompts import ChatPromptTemplate
 
# 初始化本地 LLM
llm = OllamaLLM(base_url="http://127.0.0.1:11434", model="huihui_ai/deepseek-r1-abliterated:8b")
 
# 发送对话请求
response = llm.invoke("介绍一下 LangChain 的核心功能")
print(response)
